[{"/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.js": "1", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js": "2", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useMicrophone.js": "3", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useVoiceSocket.js": "4", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx": "5", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallStatus.jsx": "6", "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js": "7"}, {"size": 254, "mtime": 1753606718566, "results": "8", "hashOfConfig": "9"}, {"size": 11660, "mtime": 1753606969035, "results": "10", "hashOfConfig": "9"}, {"size": 6929, "mtime": 1753607173424, "results": "11", "hashOfConfig": "9"}, {"size": 10207, "mtime": 1753607102677, "results": "12", "hashOfConfig": "9"}, {"size": 8190, "mtime": 1753606862688, "results": "13", "hashOfConfig": "9"}, {"size": 10697, "mtime": 1753606915721, "results": "14", "hashOfConfig": "9"}, {"size": 8908, "mtime": 1753607022476, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bjvwel", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/index.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/App.js", ["37"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useMicrophone.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/hooks/useVoiceSocket.js", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallControls.jsx", [], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/components/CallStatus.jsx", ["38", "39"], [], "/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/src/utils/audioProcessor.js", ["40"], [], {"ruleId": "41", "severity": 1, "message": "42", "line": 44, "column": 19, "nodeType": "43", "messageId": "44", "endLine": 44, "endColumn": 34}, {"ruleId": "41", "severity": 1, "message": "45", "line": 7, "column": 3, "nodeType": "43", "messageId": "44", "endLine": 7, "endColumn": 8}, {"ruleId": "41", "severity": 1, "message": "46", "line": 9, "column": 3, "nodeType": "43", "messageId": "44", "endLine": 9, "endColumn": 14}, {"ruleId": "41", "severity": 1, "message": "47", "line": 8, "column": 7, "nodeType": "43", "messageId": "44", "endLine": 8, "endColumn": 16}, "no-unused-vars", "'isMicRequesting' is assigned a value but never used.", "Identifier", "unusedVar", "'Clock' is defined but never used.", "'CheckCircle' is defined but never used.", "'MULAW_MAX' is assigned a value but never used."]