{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowUpLeftFromCircle = createLucideIcon(\"ArrowUpLeftFromCircle\", [[\"path\", {\n  d: \"M2 8V2h6\",\n  key: \"hiwtdz\"\n}], [\"path\", {\n  d: \"m2 2 10 10\",\n  key: \"1oh8rs\"\n}], [\"path\", {\n  d: \"M12 2A10 10 0 1 1 2 12\",\n  key: \"rrk4fa\"\n}]]);\nexport { ArrowUpLeftFromCircle as default };", "map": {"version": 3, "names": ["ArrowUpLeftFromCircle", "createLucideIcon", "d", "key"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/lucide-react/src/icons/arrow-up-left-from-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowUpLeftFromCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiA4VjJoNiIgLz4KICA8cGF0aCBkPSJtMiAyIDEwIDEwIiAvPgogIDxwYXRoIGQ9Ik0xMiAyQTEwIDEwIDAgMSAxIDIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-up-left-from-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowUpLeftFromCircle = createLucideIcon('ArrowUpLeftFromCircle', [\n  ['path', { d: 'M2 8V2h6', key: 'hiwtdz' }],\n  ['path', { d: 'm2 2 10 10', key: '1oh8rs' }],\n  ['path', { d: 'M12 2A10 10 0 1 1 2 12', key: 'rrk4fa' }],\n]);\n\nexport default ArrowUpLeftFromCircle;\n"], "mappings": ";;;;;AAaM,MAAAA,qBAAA,GAAwBC,gBAAA,CAAiB,uBAAyB,GACtE,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}