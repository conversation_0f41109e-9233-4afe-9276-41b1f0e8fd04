{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst RockingChair = createLucideIcon(\"RockingChair\", [[\"polyline\", {\n  points: \"3.5 2 6.5 12.5 18 12.5\",\n  key: \"y3iy52\"\n}], [\"line\", {\n  x1: \"9.5\",\n  x2: \"5.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"19vg5i\"\n}], [\"line\", {\n  x1: \"15\",\n  x2: \"18.5\",\n  y1: \"12.5\",\n  y2: \"20\",\n  key: \"1inpmv\"\n}], [\"path\", {\n  d: \"M2.75 18a13 13 0 0 0 18.5 0\",\n  key: \"1nquas\"\n}]]);\nexport { RockingChair as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/lucide-react/src/icons/rocking-chair.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name RockingChair\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIzLjUgMiA2LjUgMTIuNSAxOCAxMi41IiAvPgogIDxsaW5lIHgxPSI5LjUiIHgyPSI1LjUiIHkxPSIxMi41IiB5Mj0iMjAiIC8+CiAgPGxpbmUgeDE9IjE1IiB4Mj0iMTguNSIgeTE9IjEyLjUiIHkyPSIyMCIgLz4KICA8cGF0aCBkPSJNMi43NSAxOGExMyAxMyAwIDAgMCAxOC41IDAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/rocking-chair\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RockingChair = createLucideIcon('RockingChair', [\n  ['polyline', { points: '3.5 2 6.5 12.5 18 12.5', key: 'y3iy52' }],\n  ['line', { x1: '9.5', x2: '5.5', y1: '12.5', y2: '20', key: '19vg5i' }],\n  ['line', { x1: '15', x2: '18.5', y1: '12.5', y2: '20', key: '1inpmv' }],\n  ['path', { d: 'M2.75 18a13 13 0 0 0 18.5 0', key: '1nquas' }],\n]);\n\nexport default RockingChair;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,UAAY;EAAEC,MAAA,EAAQ,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,KAAO;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAEK,CAAA,EAAG,6BAA+B;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}