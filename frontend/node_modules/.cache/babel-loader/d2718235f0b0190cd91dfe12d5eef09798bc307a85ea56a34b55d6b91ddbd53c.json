{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst TrendingDown = createLucideIcon(\"TrendingDown\", [[\"polyline\", {\n  points: \"22 17 13.5 8.5 8.5 13.5 2 7\",\n  key: \"1r2t7k\"\n}], [\"polyline\", {\n  points: \"16 17 22 17 22 11\",\n  key: \"11uiuu\"\n}]]);\nexport { TrendingDown as default };", "map": {"version": 3, "names": ["TrendingDown", "createLucideIcon", "points", "key"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/lucide-react/src/icons/trending-down.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name TrendingDown\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSIyMiAxNyAxMy41IDguNSA4LjUgMTMuNSAyIDciIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTYgMTcgMjIgMTcgMjIgMTEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/trending-down\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingDown = createLucideIcon('TrendingDown', [\n  ['polyline', { points: '22 17 13.5 8.5 8.5 13.5 2 7', key: '1r2t7k' }],\n  ['polyline', { points: '16 17 22 17 22 11', key: '11uiuu' }],\n]);\n\nexport default TrendingDown;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CAAC,UAAY;EAAEC,MAAA,EAAQ,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,UAAY;EAAED,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}