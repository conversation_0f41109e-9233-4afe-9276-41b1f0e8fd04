{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Wifi = createLucideIcon(\"Wifi\", [[\"path\", {\n  d: \"M5 13a10 10 0 0 1 14 0\",\n  key: \"6v8j51\"\n}], [\"path\", {\n  d: \"M8.5 16.5a5 5 0 0 1 7 0\",\n  key: \"sej527\"\n}], [\"path\", {\n  d: \"M2 8.82a15 15 0 0 1 20 0\",\n  key: \"dnpr2z\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12.01\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"of4bc4\"\n}]]);\nexport { Wifi as default };", "map": {"version": 3, "names": ["Wifi", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/lucide-react/src/icons/wifi.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Wifi\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxM2ExMCAxMCAwIDAgMSAxNCAwIiAvPgogIDxwYXRoIGQ9Ik04LjUgMTYuNWE1IDUgMCAwIDEgNyAwIiAvPgogIDxwYXRoIGQ9Ik0yIDguODJhMTUgMTUgMCAwIDEgMjAgMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjIwIiB5Mj0iMjAiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/wifi\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Wifi = createLucideIcon('Wifi', [\n  ['path', { d: 'M5 13a10 10 0 0 1 14 0', key: '6v8j51' }],\n  ['path', { d: 'M8.5 16.5a5 5 0 0 1 7 0', key: 'sej527' }],\n  ['path', { d: 'M2 8.82a15 15 0 0 1 20 0', key: 'dnpr2z' }],\n  ['line', { x1: '12', x2: '12.01', y1: '20', y2: '20', key: 'of4bc4' }],\n]);\n\nexport default Wifi;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}