{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Train = createLucideIcon(\"Train\", [[\"rect\", {\n  width: \"16\",\n  height: \"16\",\n  x: \"4\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"1wxw4b\"\n}], [\"path\", {\n  d: \"M4 11h16\",\n  key: \"mpoxn0\"\n}], [\"path\", {\n  d: \"M12 3v8\",\n  key: \"1h2ygw\"\n}], [\"path\", {\n  d: \"m8 19-2 3\",\n  key: \"13i0xs\"\n}], [\"path\", {\n  d: \"m18 22-2-3\",\n  key: \"1p0ohu\"\n}], [\"path\", {\n  d: \"M8 15h0\",\n  key: \"q9eq1f\"\n}], [\"path\", {\n  d: \"M16 15h0\",\n  key: \"pzrbjg\"\n}]]);\nexport { Train as default };", "map": {"version": 3, "names": ["Train", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["/home/<USER>/PycharmProjects/ai-voice-mate-dev/ai_voice_mate/frontend/node_modules/lucide-react/src/icons/train.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Train\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHg9IjQiIHk9IjMiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Ik00IDExaDE2IiAvPgogIDxwYXRoIGQ9Ik0xMiAzdjgiIC8+CiAgPHBhdGggZD0ibTggMTktMiAzIiAvPgogIDxwYXRoIGQ9Im0xOCAyMi0yLTMiIC8+CiAgPHBhdGggZD0iTTggMTVoMCIgLz4KICA8cGF0aCBkPSJNMTYgMTVoMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/train\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Train = createLucideIcon('Train', [\n  [\n    'rect',\n    { width: '16', height: '16', x: '4', y: '3', rx: '2', key: '1wxw4b' },\n  ],\n  ['path', { d: 'M4 11h16', key: 'mpoxn0' }],\n  ['path', { d: 'M12 3v8', key: '1h2ygw' }],\n  ['path', { d: 'm8 19-2 3', key: '13i0xs' }],\n  ['path', { d: 'm18 22-2-3', key: '1p0ohu' }],\n  ['path', { d: 'M8 15h0', key: 'q9eq1f' }],\n  ['path', { d: 'M16 15h0', key: 'pzrbjg' }],\n]);\n\nexport default Train;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}